.app {
  display: flex;
  min-height: 100vh;
  width: 100vw;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  background-color: #f9fafb;
  transition: background-color 0.3s ease;
}

/* Dark theme app container */
[data-theme="dark"] .app {
  background-color: #0f172a;
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  background-color: #1e293b;
  color: white;
  padding: 20px 0;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 0 20px 20px;
  border-bottom: 1px solid #334155;
  margin-bottom: 20px;
}

.app-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #3b82f6;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 8px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #9ca3af;
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 0 25px 25px 0;
  margin-right: 8px;
}

.nav-link:hover {
  background-color: #374151;
  color: #ffffff;
}

.nav-link.active {
  background-color: #3b82f6;
  color: #ffffff;
}

.nav-icon {
  font-size: 20px;
  margin-right: 12px;
  width: 24px;
  text-align: center;
}

.nav-label {
  font-size: 16px;
  font-weight: 500;
}

/* Main Content Styles */
.main-content {
  flex: 1;
  background-color: #f9fafb;
  padding: 20px;
  overflow-y: auto;
}

/* Edge-to-edge content for Products screen */
.main-content.products {
  padding-left: 0;
  padding-right: 0;
}

/* Header Styles */
.main-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  margin-bottom: 0;
  border-bottom: 1px solid #e5e7eb;
}

/* Remove header gutter on Products screen */
.main-content.products .main-header {
  margin-bottom: 0;
  border-bottom: none;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #1f2937;
}

/* Three Dots Menu Styles */
.menu-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
}

/* Hide global three dots button on Products screen since it's integrated into the header */
.products-screen .menu-container {
  display: none;
}

.three-dots-btn {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  font-size: 20px;
  cursor: pointer;
  color: #475569;
  padding: 12px 16px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  font-weight: 500;
  min-width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.three-dots-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.three-dots-btn:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-color: #cbd5e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.three-dots-btn:hover::before {
  left: 100%;
}

.three-dots-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.three-dots-btn:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Always visible animation */
@keyframes alwaysVisiblePulse {
  0%, 100% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  }
  50% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.three-dots-btn {
  animation: alwaysVisiblePulse 3s ease-in-out infinite;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  z-index: 10000;
  min-width: 160px;
  padding: 8px 0;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 12px 20px;
  background: none;
  border: none;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 6px;
  margin: 2px 8px;
  position: relative;
}

.dropdown-item:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  transform: translateX(2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dropdown-item:active {
  transform: translateX(1px);
}

/* Dark theme adjustments */
[data-theme="dark"] .sidebar {
  background-color: #0f172a;
  border-right-color: #334155;
}

[data-theme="dark"] .main-content {
  background-color: #0f172a;
  color: #f1f5f9;
}

[data-theme="dark"] .main-content.products {
  padding-left: 0;
  padding-right: 0;
}

[data-theme="dark"] .nav-link {
  color: #94a3b8;
}

[data-theme="dark"] .nav-link:hover {
  background-color: #1e293b;
  color: #f1f5f9;
}

[data-theme="dark"] .nav-link.active {
  background-color: #60a5fa;
  color: #0f172a;
}

/* Dark theme header styles */
[data-theme="dark"] .main-header {
  border-bottom-color: #374151;
}

[data-theme="dark"] .page-title {
  color: #f1f5f9;
}

[data-theme="dark"] .three-dots-btn {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-color: #475569;
  color: #cbd5e1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .three-dots-btn:hover {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
  border-color: #64748b;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4), 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .three-dots-btn:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
}

[data-theme="dark"] .dropdown-menu {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-color: #475569;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .dropdown-item {
  color: #cbd5e1;
}

[data-theme="dark"] .dropdown-item:hover {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    padding: 0;
    z-index: 1000;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
  }

  .sidebar-header {
    display: none;
  }

  .nav-list {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 100%;
  }

  .nav-item {
    margin-bottom: 0;
    flex: 1;
  }

  .nav-link {
    flex-direction: column;
    padding: 8px;
    margin: 0;
    border-radius: 8px;
    font-size: 12px;
  }

  .nav-icon {
    margin-right: 0;
    margin-bottom: 4px;
    font-size: 18px;
  }

  .nav-label {
    font-size: 10px;
  }

  .main-content {
    padding-bottom: 80px; /* Account for fixed bottom nav */
  }

  .main-header {
    padding: 12px 0;
  }

  .page-title {
    font-size: 20px;
  }

  .three-dots-btn {
    font-size: 18px;
    padding: 10px 12px;
    min-width: 44px;
    height: 44px;
  }

  .dropdown-menu {
    min-width: 140px;
    top: calc(100% + 6px);
  }

  .dropdown-item {
    padding: 10px 16px;
    font-size: 13px;
    margin: 1px 6px;
  }

  /* Three dots menu remains visible on mobile */
  .menu-container {
    top: 15px;
    right: 15px;
  }

  .products-screen .menu-container {
    top: 10px;
    right: 10px;
  }

  .three-dots-btn {
    min-width: 40px;
    height: 40px;
    font-size: 16px;
    padding: 8px 10px;
  }
}